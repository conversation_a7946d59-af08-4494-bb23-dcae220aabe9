<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}تطبيق الأسئلة والأجوبة{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: bold;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        
        .progress {
            height: 8px;
            border-radius: 10px;
        }
        
        .navbar {
            background: rgba(255,255,255,0.1) !important;
            backdrop-filter: blur(10px);
        }
        
        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }
        
        .nav-link {
            color: white !important;
        }
        
        .nav-link:hover {
            color: #f8f9fa !important;
        }
        
        .question-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .option-btn {
            width: 100%;
            text-align: right;
            margin: 10px 0;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            background: white;
            transition: all 0.3s ease;
        }
        
        .option-btn:hover {
            border-color: #667eea;
            background: #f8f9fa;
            transform: translateX(-5px);
        }
        
        .option-btn.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .correct-answer {
            border-color: #28a745 !important;
            background: #d4edda !important;
            color: #155724 !important;
        }
        
        .wrong-answer {
            border-color: #dc3545 !important;
            background: #f8d7da !important;
            color: #721c24 !important;
        }
        
        .result-card {
            text-align: center;
            padding: 40px;
        }
        
        .score-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-brain me-2"></i>
                تطبيق الأسئلة والأجوبة
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-robot me-1"></i>
                        الذكاء الاصطناعي
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('ai_chat') }}">
                            <i class="fas fa-comments me-2"></i>المحادثة الذكية
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('ai_questions') }}">
                            <i class="fas fa-brain me-2"></i>الأسئلة المولدة
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('ai_settings') }}">
                            <i class="fas fa-cogs me-2"></i>الإعدادات
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('ai_help') }}">
                            <i class="fas fa-question-circle me-2"></i>المساعدة
                        </a></li>
                    </ul>
                </div>
                <a class="nav-link" href="{{ url_for('leaderboard') }}">
                    <i class="fas fa-trophy me-1"></i>
                    لوحة المتصدرين
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
