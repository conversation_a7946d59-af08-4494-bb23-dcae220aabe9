from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import sqlite3
import json
import random
from datetime import datetime, timedelta
import requests
import re
import time
from collections import deque
from ai_config import get_ai_config, is_ai_enabled, save_ai_config, update_ai_config, get_system_message

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # غير هذا المفتاح في الإنتاج

# نظام تتبع الطلبات لتجنب Rate Limits
request_tracker = deque()
last_request_time = None

# وظائف الذكاء الاصطناعي
def check_rate_limit():
    """التحقق من حدود معدل الطلبات"""
    global request_tracker, last_request_time
    config = get_ai_config()

    current_time = datetime.now()
    max_requests = config.get('max_requests_per_minute', 20)

    # إزالة الطلبات القديمة (أكثر من دقيقة)
    cutoff_time = current_time - timedelta(minutes=1)
    while request_tracker and request_tracker[0] < cutoff_time:
        request_tracker.popleft()

    # التحقق من عدد الطلبات
    if len(request_tracker) >= max_requests:
        return False, "تم تجاوز الحد المسموح من الطلبات (20 طلب/دقيقة). انتظر قليلاً"

    # التحقق من التأخير بين الطلبات
    rate_limit_delay = config.get('rate_limit_delay', 1)
    if last_request_time and (current_time - last_request_time).total_seconds() < rate_limit_delay:
        return False, f"يرجى الانتظار {rate_limit_delay} ثانية بين الطلبات"

    return True, None

def record_request():
    """تسجيل طلب جديد"""
    global request_tracker, last_request_time
    current_time = datetime.now()
    request_tracker.append(current_time)
    last_request_time = current_time

def get_ai_response(question, context="", message_type="conversational"):
    """الحصول على إجابة من الذكاء الاصطناعي"""
    config = get_ai_config()

    if not is_ai_enabled():
        return "عذراً، خدمة الذكاء الاصطناعي غير متوفرة حالياً. يرجى التحقق من الإعدادات."

    # التحقق من حدود معدل الطلبات
    can_proceed, error_msg = check_rate_limit()
    if not can_proceed:
        return f"⏳ {error_msg}"

    try:
        # تسجيل الطلب
        record_request()

        if config.get('provider') == 'openai':
            return get_openai_response(question, context, message_type)
        elif config.get('provider') == 'gemini':
            return get_gemini_response(question, context, message_type)
        else:
            return "مزود الذكاء الاصطناعي غير مدعوم."
    except Exception as e:
        return f"حدث خطأ في الحصول على الإجابة: {str(e)}"

def get_openai_response(question, context="", message_type="conversational"):
    """الحصول على إجابة من OpenAI مع إعادة المحاولة"""
    config = get_ai_config()

    headers = config.get_headers()

    prompt = f"""أجب على السؤال التالي باللغة العربية بشكل واضح ومختصر:

السؤال: {question}

{f"السياق: {context}" if context else ""}

الإجابة:"""

    data = {
        'model': config.get('model'),
        'messages': [
            {'role': 'system', 'content': get_system_message(message_type)},
            {'role': 'user', 'content': prompt}
        ],
        'max_tokens': config.get('max_tokens'),
        'temperature': config.get('temperature')
    }

    api_url = config.get_api_url()
    timeout = config.get('timeout', 30)
    max_retries = config.get('max_retries', 3)

    for attempt in range(max_retries):
        try:
            response = requests.post(api_url, headers=headers, json=data, timeout=timeout)

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            elif response.status_code == 401:
                return "❌ خطأ في المصادقة: تحقق من مفتاح API في الإعدادات"
            elif response.status_code == 429:
                # معالجة خاصة لخطأ تجاوز الحد
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)  # Exponential backoff
                    print(f"Rate limit hit, waiting {wait_time:.2f} seconds...")
                    time.sleep(wait_time)
                    continue
                else:
                    return "⏳ تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار دقيقة واحدة ثم المحاولة مرة أخرى."
            elif response.status_code == 403:
                return "🚫 ليس لديك صلاحية للوصول لهذا النموذج. تحقق من اشتراكك في OpenAI"
            elif response.status_code == 500:
                if attempt < max_retries - 1:
                    time.sleep(1)
                    continue
                else:
                    return "🔧 خطأ في خادم OpenAI. حاول مرة أخرى لاحقاً"
            else:
                error_msg = "خطأ غير معروف"
                try:
                    error_data = response.json()
                    if 'error' in error_data and 'message' in error_data['error']:
                        error_msg = error_data['error']['message']
                except:
                    pass
                return f"❌ خطأ في API ({response.status_code}): {error_msg}"

        except requests.exceptions.Timeout:
            if attempt < max_retries - 1:
                time.sleep(1)
                continue
            else:
                return "⏰ انتهت مهلة الاتصال. تحقق من اتصال الإنترنت"
        except requests.exceptions.ConnectionError:
            return "🌐 خطأ في الاتصال. تحقق من اتصال الإنترنت"
        except Exception as e:
            return f"❌ خطأ غير متوقع: {str(e)}"

    return "❌ فشل في الحصول على الإجابة بعد عدة محاولات"

def get_gemini_response(question, context="", message_type="conversational"):
    """الحصول على إجابة من Google Gemini"""
    # يمكن إضافة دعم Gemini هنا
    return "دعم Gemini قيد التطوير."

def generate_ai_question(topic="عام", difficulty="متوسط"):
    """توليد سؤال جديد باستخدام الذكاء الاصطناعي"""
    config = get_ai_config()

    if not is_ai_enabled():
        return None

    prompt = f"""أنشئ سؤال {difficulty} في موضوع {topic} باللغة العربية.

يجب أن يكون السؤال:
- واضح ومفهوم
- مناسب للمستوى {difficulty}
- له إجابة محددة وصحيحة

أعطني السؤال والإجابة الصحيحة بالتنسيق التالي:
السؤال: [نص السؤال]
الإجابة: [الإجابة الصحيحة]"""

    try:
        headers = config.get_headers()

        data = {
            'model': config.get('model'),
            'messages': [
                {'role': 'system', 'content': get_system_message('quiz_generator')},
                {'role': 'user', 'content': prompt}
            ],
            'max_tokens': min(config.get('max_tokens', 150) + 50, 300),  # زيادة قليلة لتوليد الأسئلة
            'temperature': min(config.get('temperature', 0.7) + 0.1, 1.0)  # زيادة الإبداع قليلاً
        }

        api_url = config.get_api_url()
        timeout = config.get('timeout', 30)

        response = requests.post(api_url, headers=headers, json=data, timeout=timeout)

        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content'].strip()

            # استخراج السؤال والإجابة
            question_match = re.search(r'السؤال:\s*(.+)', content)
            answer_match = re.search(r'الإجابة:\s*(.+)', content)

            if question_match and answer_match:
                return {
                    'question': question_match.group(1).strip(),
                    'answer': answer_match.group(1).strip(),
                    'topic': topic,
                    'difficulty': difficulty
                }

        return None
    except Exception as e:
        print(f"خطأ في توليد السؤال: {e}")
        return None

# إعداد قاعدة البيانات
def init_db():
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    
    # جدول الأسئلة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question TEXT NOT NULL,
            question_type TEXT NOT NULL,  -- 'true_false' أو 'multiple_choice'
            correct_answer TEXT NOT NULL,
            option_a TEXT,
            option_b TEXT,
            option_c TEXT,
            option_d TEXT,
            difficulty TEXT DEFAULT 'medium',
            category TEXT DEFAULT 'عام',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول النتائج
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_name TEXT,
            score INTEGER,
            total_questions INTEGER,
            percentage REAL,
            time_taken INTEGER,  -- بالثواني
            completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # جدول الأسئلة المولدة بالذكاء الاصطناعي
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ai_questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question TEXT NOT NULL,
            answer TEXT NOT NULL,
            topic TEXT DEFAULT 'عام',
            difficulty TEXT DEFAULT 'متوسط',
            source TEXT DEFAULT 'AI',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            used_count INTEGER DEFAULT 0
        )
    ''')

    # جدول محادثات الذكاء الاصطناعي
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ai_conversations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_question TEXT NOT NULL,
            ai_response TEXT NOT NULL,
            user_name TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()

# إضافة أسئلة تجريبية
def add_sample_questions():
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    
    # التحقق من وجود أسئلة
    cursor.execute('SELECT COUNT(*) FROM questions')
    if cursor.fetchone()[0] > 0:
        conn.close()
        return
    
    sample_questions = [
        # أسئلة صح وخطأ
        {
            'question': 'القاهرة هي عاصمة مصر',
            'type': 'true_false',
            'correct': 'true',
            'category': 'جغرافيا'
        },
        {
            'question': 'الأرض مسطحة',
            'type': 'true_false',
            'correct': 'false',
            'category': 'علوم'
        },
        {
            'question': 'يوجد 24 ساعة في اليوم الواحد',
            'type': 'true_false',
            'correct': 'true',
            'category': 'عام'
        },
        
        # أسئلة اختيار متعدد
        {
            'question': 'ما هي عاصمة السعودية؟',
            'type': 'multiple_choice',
            'correct': 'الرياض',
            'options': ['الرياض', 'جدة', 'الدمام', 'مكة'],
            'category': 'جغرافيا'
        },
        {
            'question': 'كم عدد أيام السنة الميلادية؟',
            'type': 'multiple_choice',
            'correct': '365',
            'options': ['364', '365', '366', '367'],
            'category': 'عام'
        },
        {
            'question': 'ما هو أكبر كوكب في النظام الشمسي؟',
            'type': 'multiple_choice',
            'correct': 'المشتري',
            'options': ['الأرض', 'المشتري', 'زحل', 'المريخ'],
            'category': 'علوم'
        }
    ]
    
    for q in sample_questions:
        if q['type'] == 'true_false':
            cursor.execute('''
                INSERT INTO questions (question, question_type, correct_answer, category)
                VALUES (?, ?, ?, ?)
            ''', (q['question'], q['type'], q['correct'], q['category']))
        else:
            cursor.execute('''
                INSERT INTO questions (question, question_type, correct_answer, 
                                     option_a, option_b, option_c, option_d, category)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (q['question'], q['type'], q['correct'], 
                  q['options'][0], q['options'][1], q['options'][2], q['options'][3], q['category']))
    
    conn.commit()
    conn.close()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/start_quiz', methods=['POST'])
def start_quiz():
    user_name = request.form.get('user_name', 'مجهول')
    num_questions = int(request.form.get('num_questions', 5))
    
    # الحصول على أسئلة عشوائية
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM questions ORDER BY RANDOM() LIMIT ?', (num_questions,))
    questions = cursor.fetchall()
    conn.close()
    
    # تحويل الأسئلة إلى قاموس
    quiz_questions = []
    for q in questions:
        question_dict = {
            'id': q[0],
            'question': q[1],
            'type': q[2],
            'correct_answer': q[3],
            'options': [q[4], q[5], q[6], q[7]] if q[2] == 'multiple_choice' else None,
            'category': q[9]
        }
        quiz_questions.append(question_dict)
    
    session['quiz_questions'] = quiz_questions
    session['current_question'] = 0
    session['score'] = 0
    session['user_name'] = user_name
    session['start_time'] = datetime.now().timestamp()
    
    return redirect(url_for('quiz'))

@app.route('/quiz')
def quiz():
    if 'quiz_questions' not in session:
        return redirect(url_for('index'))
    
    current_q = session['current_question']
    questions = session['quiz_questions']
    
    if current_q >= len(questions):
        return redirect(url_for('results'))
    
    question = questions[current_q]
    progress = ((current_q + 1) / len(questions)) * 100
    
    return render_template('quiz.html', 
                         question=question, 
                         question_num=current_q + 1,
                         total_questions=len(questions),
                         progress=progress)

@app.route('/submit_answer', methods=['POST'])
def submit_answer():
    if 'quiz_questions' not in session:
        return jsonify({'error': 'لا يوجد اختبار نشط'})
    
    user_answer = request.json.get('answer')
    current_q = session['current_question']
    questions = session['quiz_questions']
    
    if current_q >= len(questions):
        return jsonify({'error': 'انتهى الاختبار'})
    
    question = questions[current_q]
    is_correct = user_answer == question['correct_answer']
    
    if is_correct:
        session['score'] += 1
    
    session['current_question'] += 1
    
    return jsonify({
        'correct': is_correct,
        'correct_answer': question['correct_answer'],
        'next_question': session['current_question'] < len(questions)
    })

@app.route('/results')
def results():
    if 'quiz_questions' not in session:
        return redirect(url_for('index'))
    
    score = session['score']
    total = len(session['quiz_questions'])
    percentage = (score / total) * 100
    time_taken = int(datetime.now().timestamp() - session['start_time'])
    
    # حفظ النتيجة في قاعدة البيانات
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO results (user_name, score, total_questions, percentage, time_taken)
        VALUES (?, ?, ?, ?, ?)
    ''', (session['user_name'], score, total, percentage, time_taken))
    conn.commit()
    conn.close()
    
    # مسح بيانات الجلسة
    session.clear()
    
    return render_template('results.html', 
                         score=score, 
                         total=total, 
                         percentage=percentage,
                         time_taken=time_taken)

@app.route('/leaderboard')
def leaderboard():
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT user_name, score, total_questions, percentage, time_taken, completed_at
        FROM results
        ORDER BY percentage DESC, time_taken ASC
        LIMIT 10
    ''')
    top_results = cursor.fetchall()
    conn.close()

    return render_template('leaderboard.html', results=top_results)

@app.route('/ai_chat')
def ai_chat():
    """صفحة المحادثة مع الذكاء الاصطناعي"""
    return render_template('ai_chat.html')

@app.route('/ask_ai', methods=['POST'])
def ask_ai():
    """إرسال سؤال للذكاء الاصطناعي"""
    data = request.get_json()
    question = data.get('question', '').strip()
    user_name = session.get('user_name', 'مجهول')

    if not question:
        return jsonify({'error': 'يرجى إدخال سؤال'})

    # الحصول على إجابة من الذكاء الاصطناعي
    ai_response = get_ai_response(question)

    # حفظ المحادثة في قاعدة البيانات
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO ai_conversations (user_question, ai_response, user_name)
        VALUES (?, ?, ?)
    ''', (question, ai_response, user_name))
    conn.commit()
    conn.close()

    return jsonify({
        'question': question,
        'answer': ai_response,
        'timestamp': datetime.now().strftime('%H:%M')
    })

@app.route('/generate_question', methods=['POST'])
def generate_question():
    """توليد سؤال جديد باستخدام الذكاء الاصطناعي"""
    data = request.get_json()
    topic = data.get('topic', 'عام')
    difficulty = data.get('difficulty', 'متوسط')

    # توليد السؤال
    generated = generate_ai_question(topic, difficulty)

    if generated:
        # حفظ السؤال في قاعدة البيانات
        conn = sqlite3.connect('quiz_app.db')
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO ai_questions (question, answer, topic, difficulty)
            VALUES (?, ?, ?, ?)
        ''', (generated['question'], generated['answer'], topic, difficulty))
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'question': generated['question'],
            'answer': generated['answer'],
            'topic': topic,
            'difficulty': difficulty
        })
    else:
        return jsonify({
            'success': False,
            'error': 'فشل في توليد السؤال'
        })

@app.route('/ai_questions')
def ai_questions():
    """عرض الأسئلة المولدة بالذكاء الاصطناعي"""
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT id, question, answer, topic, difficulty, created_at, used_count
        FROM ai_questions
        ORDER BY created_at DESC
        LIMIT 50
    ''')
    questions = cursor.fetchall()
    conn.close()

    return render_template('ai_questions.html', questions=questions)

@app.route('/ai_settings')
def ai_settings():
    """صفحة إعدادات الذكاء الاصطناعي"""
    config = get_ai_config()
    return render_template('ai_settings.html', config=config.config)

@app.route('/ai_help')
def ai_help():
    """صفحة مساعدة الذكاء الاصطناعي"""
    return render_template('ai_help.html')

@app.route('/update_ai_settings', methods=['POST'])
def update_ai_settings():
    """تحديث إعدادات الذكاء الاصطناعي"""
    new_config = {
        'enabled': request.form.get('enabled') == 'on',
        'provider': request.form.get('provider', 'openai'),
        'api_key': request.form.get('api_key', '').strip(),
        'model': request.form.get('model', 'gpt-3.5-turbo'),
        'max_tokens': int(request.form.get('max_tokens', 150)),
        'temperature': float(request.form.get('temperature', 0.7)),
        'rate_limit_delay': float(request.form.get('rate_limit_delay', 1)),
        'max_requests_per_minute': int(request.form.get('max_requests_per_minute', 20)),
        'use_exponential_backoff': request.form.get('use_exponential_backoff') == 'on'
    }

    # التحقق من صحة الإعدادات
    config = get_ai_config()
    config.update(new_config)
    errors = config.validate_config()

    if errors:
        # يمكن إضافة عرض الأخطاء هنا
        pass

    # حفظ الإعدادات
    success = save_ai_config()

    return redirect(url_for('ai_settings'))

if __name__ == '__main__':
    init_db()
    add_sample_questions()
    app.run(debug=True, host='0.0.0.0', port=5000)
