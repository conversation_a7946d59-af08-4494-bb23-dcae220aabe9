from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import sqlite3
import json
import random
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # غير هذا المفتاح في الإنتاج

# إعداد قاعدة البيانات
def init_db():
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    
    # جدول الأسئلة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS questions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question TEXT NOT NULL,
            question_type TEXT NOT NULL,  -- 'true_false' أو 'multiple_choice'
            correct_answer TEXT NOT NULL,
            option_a TEXT,
            option_b TEXT,
            option_c TEXT,
            option_d TEXT,
            difficulty TEXT DEFAULT 'medium',
            category TEXT DEFAULT 'عام',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول النتائج
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_name TEXT,
            score INTEGER,
            total_questions INTEGER,
            percentage REAL,
            time_taken INTEGER,  -- بالثواني
            completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()

# إضافة أسئلة تجريبية
def add_sample_questions():
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    
    # التحقق من وجود أسئلة
    cursor.execute('SELECT COUNT(*) FROM questions')
    if cursor.fetchone()[0] > 0:
        conn.close()
        return
    
    sample_questions = [
        # أسئلة صح وخطأ
        {
            'question': 'القاهرة هي عاصمة مصر',
            'type': 'true_false',
            'correct': 'true',
            'category': 'جغرافيا'
        },
        {
            'question': 'الأرض مسطحة',
            'type': 'true_false',
            'correct': 'false',
            'category': 'علوم'
        },
        {
            'question': 'يوجد 24 ساعة في اليوم الواحد',
            'type': 'true_false',
            'correct': 'true',
            'category': 'عام'
        },
        
        # أسئلة اختيار متعدد
        {
            'question': 'ما هي عاصمة السعودية؟',
            'type': 'multiple_choice',
            'correct': 'الرياض',
            'options': ['الرياض', 'جدة', 'الدمام', 'مكة'],
            'category': 'جغرافيا'
        },
        {
            'question': 'كم عدد أيام السنة الميلادية؟',
            'type': 'multiple_choice',
            'correct': '365',
            'options': ['364', '365', '366', '367'],
            'category': 'عام'
        },
        {
            'question': 'ما هو أكبر كوكب في النظام الشمسي؟',
            'type': 'multiple_choice',
            'correct': 'المشتري',
            'options': ['الأرض', 'المشتري', 'زحل', 'المريخ'],
            'category': 'علوم'
        }
    ]
    
    for q in sample_questions:
        if q['type'] == 'true_false':
            cursor.execute('''
                INSERT INTO questions (question, question_type, correct_answer, category)
                VALUES (?, ?, ?, ?)
            ''', (q['question'], q['type'], q['correct'], q['category']))
        else:
            cursor.execute('''
                INSERT INTO questions (question, question_type, correct_answer, 
                                     option_a, option_b, option_c, option_d, category)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (q['question'], q['type'], q['correct'], 
                  q['options'][0], q['options'][1], q['options'][2], q['options'][3], q['category']))
    
    conn.commit()
    conn.close()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/start_quiz', methods=['POST'])
def start_quiz():
    user_name = request.form.get('user_name', 'مجهول')
    num_questions = int(request.form.get('num_questions', 5))
    
    # الحصول على أسئلة عشوائية
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM questions ORDER BY RANDOM() LIMIT ?', (num_questions,))
    questions = cursor.fetchall()
    conn.close()
    
    # تحويل الأسئلة إلى قاموس
    quiz_questions = []
    for q in questions:
        question_dict = {
            'id': q[0],
            'question': q[1],
            'type': q[2],
            'correct_answer': q[3],
            'options': [q[4], q[5], q[6], q[7]] if q[2] == 'multiple_choice' else None,
            'category': q[9]
        }
        quiz_questions.append(question_dict)
    
    session['quiz_questions'] = quiz_questions
    session['current_question'] = 0
    session['score'] = 0
    session['user_name'] = user_name
    session['start_time'] = datetime.now().timestamp()
    
    return redirect(url_for('quiz'))

@app.route('/quiz')
def quiz():
    if 'quiz_questions' not in session:
        return redirect(url_for('index'))
    
    current_q = session['current_question']
    questions = session['quiz_questions']
    
    if current_q >= len(questions):
        return redirect(url_for('results'))
    
    question = questions[current_q]
    progress = ((current_q + 1) / len(questions)) * 100
    
    return render_template('quiz.html', 
                         question=question, 
                         question_num=current_q + 1,
                         total_questions=len(questions),
                         progress=progress)

@app.route('/submit_answer', methods=['POST'])
def submit_answer():
    if 'quiz_questions' not in session:
        return jsonify({'error': 'لا يوجد اختبار نشط'})
    
    user_answer = request.json.get('answer')
    current_q = session['current_question']
    questions = session['quiz_questions']
    
    if current_q >= len(questions):
        return jsonify({'error': 'انتهى الاختبار'})
    
    question = questions[current_q]
    is_correct = user_answer == question['correct_answer']
    
    if is_correct:
        session['score'] += 1
    
    session['current_question'] += 1
    
    return jsonify({
        'correct': is_correct,
        'correct_answer': question['correct_answer'],
        'next_question': session['current_question'] < len(questions)
    })

@app.route('/results')
def results():
    if 'quiz_questions' not in session:
        return redirect(url_for('index'))
    
    score = session['score']
    total = len(session['quiz_questions'])
    percentage = (score / total) * 100
    time_taken = int(datetime.now().timestamp() - session['start_time'])
    
    # حفظ النتيجة في قاعدة البيانات
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO results (user_name, score, total_questions, percentage, time_taken)
        VALUES (?, ?, ?, ?, ?)
    ''', (session['user_name'], score, total, percentage, time_taken))
    conn.commit()
    conn.close()
    
    # مسح بيانات الجلسة
    session.clear()
    
    return render_template('results.html', 
                         score=score, 
                         total=total, 
                         percentage=percentage,
                         time_taken=time_taken)

@app.route('/leaderboard')
def leaderboard():
    conn = sqlite3.connect('quiz_app.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT user_name, score, total_questions, percentage, time_taken, completed_at
        FROM results 
        ORDER BY percentage DESC, time_taken ASC 
        LIMIT 10
    ''')
    top_results = cursor.fetchall()
    conn.close()
    
    return render_template('leaderboard.html', results=top_results)

if __name__ == '__main__':
    init_db()
    add_sample_questions()
    app.run(debug=True, host='0.0.0.0', port=5000)
