{% extends "base.html" %}

{% block title %}مساعدة الذكاء الاصطناعي{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    مساعدة الذكاء الاصطناعي
                </h4>
            </div>
            
            <div class="card-body">
                <!-- مشاكل شائعة -->
                <div class="accordion" id="helpAccordion">
                    
                    <!-- مشكلة Rate Limits -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="rateLimitHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#rateLimitCollapse">
                                <i class="fas fa-tachometer-alt me-2 text-warning"></i>
                                "تم تجاوز الحد المسموح من الطلبات"
                            </button>
                        </h2>
                        <div id="rateLimitCollapse" class="accordion-collapse collapse show" 
                             data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <h6>الأسباب:</h6>
                                <ul>
                                    <li>إرسال أكثر من 20 سؤال في الدقيقة الواحدة</li>
                                    <li>عدم الانتظار ثانية واحدة بين الأسئلة</li>
                                    <li>تجاوز حدود OpenAI للحساب المجاني</li>
                                </ul>
                                
                                <h6>الحلول:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="alert alert-success">
                                            <h6><i class="fas fa-check me-2"></i>حلول فورية:</h6>
                                            <ul class="mb-0">
                                                <li>انتظر دقيقة واحدة ثم حاول مرة أخرى</li>
                                                <li>اترك ثانية واحدة بين كل سؤال والآخر</li>
                                                <li>قلل عدد الأسئلة المرسلة</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-cog me-2"></i>حلول متقدمة:</h6>
                                            <ul class="mb-0">
                                                <li>اذهب للإعدادات وزد "التأخير بين الطلبات"</li>
                                                <li>قلل "الحد الأقصى للطلبات/دقيقة"</li>
                                                <li>فعّل "التأخير المتزايد"</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- مشكلة API Key -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="apiKeyHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#apiKeyCollapse">
                                <i class="fas fa-key me-2 text-danger"></i>
                                "خطأ في المصادقة: تحقق من مفتاح API"
                            </button>
                        </h2>
                        <div id="apiKeyCollapse" class="accordion-collapse collapse" 
                             data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <h6>الأسباب:</h6>
                                <ul>
                                    <li>مفتاح API غير صحيح أو منتهي الصلاحية</li>
                                    <li>لم يتم إدخال مفتاح API</li>
                                    <li>مفتاح API محذوف من حساب OpenAI</li>
                                </ul>
                                
                                <h6>الحلول:</h6>
                                <ol>
                                    <li>اذهب إلى <a href="{{ url_for('ai_settings') }}">إعدادات الذكاء الاصطناعي</a></li>
                                    <li>تحقق من مفتاح API المدخل</li>
                                    <li>إذا لزم الأمر، أنشئ مفتاح جديد من <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI</a></li>
                                    <li>اضغط "اختبار الاتصال" للتأكد</li>
                                </ol>
                            </div>
                        </div>
                    </div>

                    <!-- مشكلة الرصيد -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="creditHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#creditCollapse">
                                <i class="fas fa-credit-card me-2 text-warning"></i>
                                "ليس لديك صلاحية للوصول لهذا النموذج"
                            </button>
                        </h2>
                        <div id="creditCollapse" class="accordion-collapse collapse" 
                             data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <h6>الأسباب:</h6>
                                <ul>
                                    <li>نفاد الرصيد في حساب OpenAI</li>
                                    <li>عدم وجود طريقة دفع مفعلة</li>
                                    <li>محاولة استخدام نموذج غير متاح للحساب</li>
                                </ul>
                                
                                <h6>الحلول:</h6>
                                <ol>
                                    <li>اذهب إلى <a href="https://platform.openai.com/account/billing" target="_blank">صفحة الفوترة</a> في OpenAI</li>
                                    <li>أضف طريقة دفع إذا لم تكن موجودة</li>
                                    <li>أضف رصيد (5$ كافية للبداية)</li>
                                    <li>جرب نموذج أقل تكلفة مثل GPT-3.5 Turbo</li>
                                </ol>
                            </div>
                        </div>
                    </div>

                    <!-- مشكلة الاتصال -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="connectionHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#connectionCollapse">
                                <i class="fas fa-wifi me-2 text-info"></i>
                                "خطأ في الاتصال" أو "انتهت مهلة الاتصال"
                            </button>
                        </h2>
                        <div id="connectionCollapse" class="accordion-collapse collapse" 
                             data-bs-parent="#helpAccordion">
                            <div class="accordion-body">
                                <h6>الأسباب:</h6>
                                <ul>
                                    <li>مشكلة في اتصال الإنترنت</li>
                                    <li>خادم OpenAI مشغول أو معطل مؤقتاً</li>
                                    <li>جدار حماية يحجب الاتصال</li>
                                </ul>
                                
                                <h6>الحلول:</h6>
                                <ol>
                                    <li>تحقق من اتصال الإنترنت</li>
                                    <li>حاول مرة أخرى بعد دقائق قليلة</li>
                                    <li>تحقق من حالة خدمات OpenAI على <a href="https://status.openai.com/" target="_blank">status.openai.com</a></li>
                                    <li>جرب استخدام VPN إذا كان متاحاً</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نصائح عامة -->
                <div class="mt-5">
                    <h5><i class="fas fa-lightbulb me-2 text-warning"></i>نصائح لتجربة أفضل</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">✅ افعل</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="mb-0">
                                        <li>انتظر ثانية بين الأسئلة</li>
                                        <li>استخدم أسئلة واضحة ومحددة</li>
                                        <li>ابدأ بـ GPT-3.5 Turbo</li>
                                        <li>راقب استهلاك الرصيد</li>
                                        <li>احفظ مفتاح API بأمان</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">❌ لا تفعل</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="mb-0">
                                        <li>لا ترسل أسئلة متتالية بسرعة</li>
                                        <li>لا تشارك مفتاح API مع أحد</li>
                                        <li>لا تستخدم نماذج غالية بلا داع</li>
                                        <li>لا تترك الرصيد بدون مراقبة</li>
                                        <li>لا تستخدم أسئلة غير واضحة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- روابط مفيدة -->
                <div class="mt-4">
                    <h6><i class="fas fa-link me-2"></i>روابط مفيدة</h6>
                    <div class="list-group">
                        <a href="{{ url_for('ai_settings') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-cogs me-2"></i>إعدادات الذكاء الاصطناعي
                        </a>
                        <a href="https://platform.openai.com/api-keys" target="_blank" class="list-group-item list-group-item-action">
                            <i class="fas fa-key me-2"></i>إدارة مفاتيح API - OpenAI
                        </a>
                        <a href="https://platform.openai.com/account/billing" target="_blank" class="list-group-item list-group-item-action">
                            <i class="fas fa-credit-card me-2"></i>إدارة الفوترة - OpenAI
                        </a>
                        <a href="https://status.openai.com/" target="_blank" class="list-group-item list-group-item-action">
                            <i class="fas fa-server me-2"></i>حالة خدمات OpenAI
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تأثير ظهور البطاقات
    $('.card').hide().fadeIn(1000);
    
    // تأثير للأيقونات
    $('.accordion-button').hover(
        function() {
            $(this).find('i').addClass('fa-bounce');
        },
        function() {
            $(this).find('i').removeClass('fa-bounce');
        }
    );
});
</script>
{% endblock %}
