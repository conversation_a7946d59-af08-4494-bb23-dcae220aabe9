{% extends "base.html" %}

{% block title %}الأسئلة المولدة بالذكاء الاصطناعي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-brain me-2"></i>
                        الأسئلة المولدة بالذكاء الاصطناعي
                    </h4>
                    <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#generateModal">
                        <i class="fas fa-plus me-2"></i>
                        توليد سؤال جديد
                    </button>
                </div>
            </div>
            
            <div class="card-body">
                {% if questions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>السؤال</th>
                                    <th>الإجابة</th>
                                    <th>الموضوع</th>
                                    <th>الصعوبة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>مرات الاستخدام</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for question in questions %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <div class="question-text">
                                                {{ question[1][:100] }}{% if question[1]|length > 100 %}...{% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="answer-text">
                                                {{ question[2][:50] }}{% if question[2]|length > 50 %}...{% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ question[3] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge 
                                                {% if question[4] == 'سهل' %}bg-success
                                                {% elif question[4] == 'متوسط' %}bg-warning
                                                {% else %}bg-danger{% endif %}">
                                                {{ question[4] }}
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ question[5][:10] }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ question[6] }}</span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1" 
                                                    onclick="viewQuestion({{ question[0] }}, '{{ question[1] }}', '{{ question[2] }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" 
                                                    onclick="addToQuiz({{ question[0] }})">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center p-5">
                        <i class="fas fa-brain fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد أسئلة مولدة بعد</h4>
                        <p class="text-muted">ابدأ بتوليد أسئلة جديدة باستخدام الذكاء الاصطناعي</p>
                        <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#generateModal">
                            <i class="fas fa-plus me-2"></i>
                            توليد سؤال جديد
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نافذة توليد سؤال جديد -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-brain me-2"></i>
                    توليد سؤال جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="generateForm">
                    <div class="mb-3">
                        <label for="topic" class="form-label">الموضوع:</label>
                        <select class="form-select" id="topic" name="topic">
                            <option value="عام">عام</option>
                            <option value="علوم">علوم</option>
                            <option value="تاريخ">تاريخ</option>
                            <option value="جغرافيا">جغرافيا</option>
                            <option value="رياضيات">رياضيات</option>
                            <option value="أدب">أدب</option>
                            <option value="رياضة">رياضة</option>
                            <option value="تكنولوجيا">تكنولوجيا</option>
                            <option value="فن">فن</option>
                            <option value="طبخ">طبخ</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="difficulty" class="form-label">مستوى الصعوبة:</label>
                        <select class="form-select" id="difficulty" name="difficulty">
                            <option value="سهل">سهل</option>
                            <option value="متوسط" selected>متوسط</option>
                            <option value="صعب">صعب</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="generateQuestion()">
                    <i class="fas fa-cog fa-spin" id="generateSpinner" style="display: none;"></i>
                    <i class="fas fa-brain" id="generateIcon"></i>
                    <span id="generateText">توليد السؤال</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض السؤال -->
<div class="modal fade" id="viewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle me-2"></i>
                    تفاصيل السؤال
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">السؤال:</label>
                    <div class="p-3 bg-light rounded" id="viewQuestion"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">الإجابة:</label>
                    <div class="p-3 bg-success bg-opacity-10 rounded" id="viewAnswer"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" onclick="addCurrentToQuiz()">
                    <i class="fas fa-plus me-2"></i>
                    إضافة للاختبار
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.question-text, .answer-text {
    max-width: 200px;
    word-wrap: break-word;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px 15px 0 0;
}

.btn-close {
    filter: invert(1);
}
</style>
{% endblock %}

{% block scripts %}
<script>
let currentQuestionId = null;

function generateQuestion() {
    const topic = $('#topic').val();
    const difficulty = $('#difficulty').val();
    
    // تغيير حالة الزر
    $('#generateSpinner').show();
    $('#generateIcon').hide();
    $('#generateText').text('جاري التوليد...');
    $('button[onclick="generateQuestion()"]').prop('disabled', true);
    
    $.ajax({
        url: '/generate_question',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            topic: topic,
            difficulty: difficulty
        }),
        success: function(response) {
            if (response.success) {
                // إغلاق النافذة وإعادة تحميل الصفحة
                $('#generateModal').modal('hide');
                location.reload();
            } else {
                alert('فشل في توليد السؤال: ' + response.error);
            }
        },
        error: function() {
            alert('حدث خطأ في توليد السؤال');
        },
        complete: function() {
            // إعادة تعيين حالة الزر
            $('#generateSpinner').hide();
            $('#generateIcon').show();
            $('#generateText').text('توليد السؤال');
            $('button[onclick="generateQuestion()"]').prop('disabled', false);
        }
    });
}

function viewQuestion(id, question, answer) {
    currentQuestionId = id;
    $('#viewQuestion').text(question);
    $('#viewAnswer').text(answer);
    $('#viewModal').modal('show');
}

function addToQuiz(questionId) {
    // هنا يمكن إضافة منطق إضافة السؤال للاختبار
    alert('تم إضافة السؤال للاختبار! (هذه الميزة قيد التطوير)');
}

function addCurrentToQuiz() {
    if (currentQuestionId) {
        addToQuiz(currentQuestionId);
        $('#viewModal').modal('hide');
    }
}

// إعادة تعيين النموذج عند إغلاق النافذة
$('#generateModal').on('hidden.bs.modal', function() {
    $('#generateForm')[0].reset();
    $('#topic').val('عام');
    $('#difficulty').val('متوسط');
});
</script>
{% endblock %}
