{% extends "base.html" %}

{% block title %}إعدادات الذكاء الاصطناعي{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات الذكاء الاصطناعي
                </h4>
            </div>
            
            <div class="card-body">
                <form action="{{ url_for('update_ai_settings') }}" method="POST">
                    <!-- تفعيل/إلغاء تفعيل الذكاء الاصطناعي -->
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enabled" name="enabled" 
                                   {% if config.enabled %}checked{% endif %}>
                            <label class="form-check-label fw-bold" for="enabled">
                                تفعيل الذكاء الاصطناعي
                            </label>
                        </div>
                        <small class="text-muted">تفعيل أو إلغاء تفعيل جميع ميزات الذكاء الاصطناعي</small>
                    </div>

                    <!-- مزود الخدمة -->
                    <div class="mb-4">
                        <label for="provider" class="form-label fw-bold">مزود الخدمة:</label>
                        <select class="form-select" id="provider" name="provider">
                            <option value="openai" {% if config.provider == 'openai' %}selected{% endif %}>
                                OpenAI (GPT)
                            </option>
                            <option value="gemini" {% if config.provider == 'gemini' %}selected{% endif %}>
                                Google Gemini (قيد التطوير)
                            </option>
                        </select>
                        <small class="text-muted">اختر مزود خدمة الذكاء الاصطناعي</small>
                    </div>

                    <!-- مفتاح API -->
                    <div class="mb-4">
                        <label for="api_key" class="form-label fw-bold">مفتاح API:</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="api_key" name="api_key" 
                                   value="{{ config.api_key }}" placeholder="أدخل مفتاح API هنا">
                            <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <small class="text-muted">
                            مفتاح API الخاص بمزود الخدمة. 
                            <a href="https://platform.openai.com/api-keys" target="_blank">احصل على مفتاح OpenAI</a>
                        </small>
                    </div>

                    <!-- النموذج -->
                    <div class="mb-4">
                        <label for="model" class="form-label fw-bold">النموذج:</label>
                        <select class="form-select" id="model" name="model">
                            <option value="gpt-3.5-turbo" {% if config.model == 'gpt-3.5-turbo' %}selected{% endif %}>
                                GPT-3.5 Turbo (سريع ومناسب)
                            </option>
                            <option value="gpt-4" {% if config.model == 'gpt-4' %}selected{% endif %}>
                                GPT-4 (أكثر دقة وذكاء)
                            </option>
                            <option value="gpt-4-turbo" {% if config.model == 'gpt-4-turbo' %}selected{% endif %}>
                                GPT-4 Turbo (متوازن)
                            </option>
                        </select>
                        <small class="text-muted">اختر نموذج الذكاء الاصطناعي المناسب</small>
                    </div>

                    <!-- الحد الأقصى للرموز -->
                    <div class="mb-4">
                        <label for="max_tokens" class="form-label fw-bold">الحد الأقصى للرموز:</label>
                        <input type="range" class="form-range" id="max_tokens" name="max_tokens" 
                               min="50" max="500" value="{{ config.max_tokens }}" 
                               oninput="updateTokensValue(this.value)">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">50</small>
                            <small class="text-muted">القيمة الحالية: <span id="tokensValue">{{ config.max_tokens }}</span></small>
                            <small class="text-muted">500</small>
                        </div>
                        <small class="text-muted">يحدد طول الإجابة (رموز أكثر = إجابات أطول وتكلفة أعلى)</small>
                    </div>

                    <!-- درجة الحرارة -->
                    <div class="mb-4">
                        <label for="temperature" class="form-label fw-bold">درجة الإبداع:</label>
                        <input type="range" class="form-range" id="temperature" name="temperature" 
                               min="0" max="1" step="0.1" value="{{ config.temperature }}" 
                               oninput="updateTemperatureValue(this.value)">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">محافظ (0)</small>
                            <small class="text-muted">القيمة الحالية: <span id="temperatureValue">{{ config.temperature }}</span></small>
                            <small class="text-muted">مبدع (1)</small>
                        </div>
                        <small class="text-muted">يحدد مدى إبداع وتنوع الإجابات</small>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-secondary me-md-2" onclick="testConnection()">
                            <i class="fas fa-plug me-2"></i>
                            اختبار الاتصال
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الإعدادات
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-info-circle fa-3x text-info mb-3"></i>
                        <h6>معلومات مهمة</h6>
                        <p class="small text-muted">
                            تأكد من الحصول على مفتاح API صالح من مزود الخدمة.
                            النماذج الأكثر تقدماً تكلف أكثر لكنها تعطي نتائج أفضل.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <h6>الأمان والخصوصية</h6>
                        <p class="small text-muted">
                            مفتاح API يُحفظ محلياً فقط ولا يُرسل لأي جهة خارجية.
                            جميع المحادثات محفوظة في قاعدة البيانات المحلية.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- حالة الاتصال -->
        <div id="connectionStatus" class="alert" style="display: none; margin-top: 20px;">
            <div class="d-flex align-items-center">
                <i id="statusIcon" class="fas fa-2x me-3"></i>
                <div>
                    <h6 id="statusTitle" class="mb-1"></h6>
                    <p id="statusMessage" class="mb-0"></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function updateTokensValue(value) {
    document.getElementById('tokensValue').textContent = value;
}

function updateTemperatureValue(value) {
    document.getElementById('temperatureValue').textContent = value;
}

function testConnection() {
    const statusDiv = $('#connectionStatus');
    const statusIcon = $('#statusIcon');
    const statusTitle = $('#statusTitle');
    const statusMessage = $('#statusMessage');
    
    // إظهار حالة الاختبار
    statusDiv.removeClass('alert-success alert-danger').addClass('alert-info').show();
    statusIcon.removeClass('fa-check-circle fa-times-circle').addClass('fa-spinner fa-spin');
    statusTitle.text('جاري اختبار الاتصال...');
    statusMessage.text('يرجى الانتظار...');
    
    // إرسال طلب اختبار
    $.ajax({
        url: '/ask_ai',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({question: 'مرحبا'}),
        success: function(response) {
            statusDiv.removeClass('alert-info alert-danger').addClass('alert-success');
            statusIcon.removeClass('fa-spinner fa-spin fa-times-circle').addClass('fa-check-circle');
            statusTitle.text('نجح الاتصال!');
            statusMessage.text('تم الاتصال بنجاح مع خدمة الذكاء الاصطناعي.');
        },
        error: function(xhr) {
            statusDiv.removeClass('alert-info alert-success').addClass('alert-danger');
            statusIcon.removeClass('fa-spinner fa-spin fa-check-circle').addClass('fa-times-circle');
            statusTitle.text('فشل الاتصال!');
            statusMessage.text('تعذر الاتصال بخدمة الذكاء الاصطناعي. تحقق من مفتاح API والإعدادات.');
        }
    });
}

// إظهار/إخفاء مفتاح API
$('#toggleApiKey').click(function() {
    const apiKeyInput = $('#api_key');
    const icon = $(this).find('i');
    
    if (apiKeyInput.attr('type') === 'password') {
        apiKeyInput.attr('type', 'text');
        icon.removeClass('fa-eye').addClass('fa-eye-slash');
    } else {
        apiKeyInput.attr('type', 'password');
        icon.removeClass('fa-eye-slash').addClass('fa-eye');
    }
});

// تحديث خيارات النموذج حسب المزود
$('#provider').change(function() {
    const provider = $(this).val();
    const modelSelect = $('#model');
    
    modelSelect.empty();
    
    if (provider === 'openai') {
        modelSelect.append('<option value="gpt-3.5-turbo">GPT-3.5 Turbo (سريع ومناسب)</option>');
        modelSelect.append('<option value="gpt-4">GPT-4 (أكثر دقة وذكاء)</option>');
        modelSelect.append('<option value="gpt-4-turbo">GPT-4 Turbo (متوازن)</option>');
    } else if (provider === 'gemini') {
        modelSelect.append('<option value="gemini-pro">Gemini Pro (قيد التطوير)</option>');
    }
});
</script>
{% endblock %}
