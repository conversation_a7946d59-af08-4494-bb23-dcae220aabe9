{% extends "base.html" %}

{% block title %}النتائج - تطبيق الأسئلة والأجوبة{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body result-card">
                <h1 class="display-4 mb-4">🎉 تهانينا! 🎉</h1>
                
                <!-- دائرة النتيجة -->
                <div class="score-circle">
                    {{ score }}/{{ total }}
                </div>
                
                <h3 class="mb-3">نتيجتك النهائية</h3>
                
                <!-- تفاصيل النتيجة -->
                <div class="row g-4 mt-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-check-circle fa-3x text-success mb-2"></i>
                            <h4 class="text-success">{{ score }}</h4>
                            <p class="text-muted">إجابات صحيحة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-times-circle fa-3x text-danger mb-2"></i>
                            <h4 class="text-danger">{{ total - score }}</h4>
                            <p class="text-muted">إجابات خاطئة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-percentage fa-3x text-primary mb-2"></i>
                            <h4 class="text-primary">{{ "%.1f"|format(percentage) }}%</h4>
                            <p class="text-muted">النسبة المئوية</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-clock fa-3x text-warning mb-2"></i>
                            <h4 class="text-warning">
                                {% set minutes = time_taken // 60 %}
                                {% set seconds = time_taken % 60 %}
                                {{ minutes }}:{{ "%02d"|format(seconds) }}
                            </h4>
                            <p class="text-muted">الوقت المستغرق</p>
                        </div>
                    </div>
                </div>

                <!-- تقييم الأداء -->
                <div class="mt-5">
                    {% if percentage >= 90 %}
                        <div class="alert alert-success">
                            <h5><i class="fas fa-star me-2"></i>ممتاز جداً!</h5>
                            <p class="mb-0">أداء رائع! لديك معرفة ممتازة في هذا المجال.</p>
                        </div>
                    {% elif percentage >= 80 %}
                        <div class="alert alert-info">
                            <h5><i class="fas fa-thumbs-up me-2"></i>جيد جداً!</h5>
                            <p class="mb-0">أداء جيد! تحتاج لمراجعة بسيطة في بعض النقاط.</p>
                        </div>
                    {% elif percentage >= 70 %}
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-hand-paper me-2"></i>جيد!</h5>
                            <p class="mb-0">أداء مقبول، لكن يمكنك تحسينه بالمزيد من الدراسة.</p>
                        </div>
                    {% elif percentage >= 50 %}
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>مقبول!</h5>
                            <p class="mb-0">تحتاج لمراجعة أكثر في هذا المجال.</p>
                        </div>
                    {% else %}
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-book me-2"></i>يحتاج تحسين!</h5>
                            <p class="mb-0">ننصحك بالمزيد من الدراسة والمراجعة.</p>
                        </div>
                    {% endif %}
                </div>

                <!-- أزرار العمل -->
                <div class="mt-5">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-redo me-2"></i>
                                اختبار جديد
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ url_for('leaderboard') }}" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-trophy me-2"></i>
                                لوحة المتصدرين
                            </a>
                        </div>
                        <div class="col-md-4">
                            <button onclick="shareResult()" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-share me-2"></i>
                                مشاركة النتيجة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تأثير ظهور النتائج
    $('.card').hide().fadeIn(1000);
    
    // تأثير دوران دائرة النتيجة
    $('.score-circle').hide().delay(500).fadeIn(500).addClass('animate__animated animate__pulse');
    
    // تأثير ظهور الإحصائيات
    $('.col-md-3').each(function(index) {
        $(this).hide().delay(1000 + (index * 200)).fadeIn(500);
    });
    
    // تأثير ظهور التقييم
    $('.alert').hide().delay(2000).slideDown(500);
    
    // تأثير ظهور الأزرار
    $('.btn').hide().delay(2500).fadeIn(500);
});

function shareResult() {
    const score = {{ score }};
    const total = {{ total }};
    const percentage = {{ "%.1f"|format(percentage) }};
    
    const text = `لقد حصلت على ${score} من ${total} (${percentage}%) في تطبيق الأسئلة والأجوبة! 🎉`;
    
    if (navigator.share) {
        navigator.share({
            title: 'نتيجة الاختبار',
            text: text,
            url: window.location.origin
        });
    } else {
        // نسخ النص للحافظة
        navigator.clipboard.writeText(text).then(function() {
            alert('تم نسخ النتيجة للحافظة!');
        });
    }
}

// تأثيرات بصرية إضافية
setInterval(function() {
    $('.score-circle').addClass('animate__animated animate__pulse');
    setTimeout(function() {
        $('.score-circle').removeClass('animate__animated animate__pulse');
    }, 1000);
}, 3000);
</script>
{% endblock %}
