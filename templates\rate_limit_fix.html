{% extends "base.html" %}

{% block title %}حل مشاكل Rate Limits{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حل سريع لمشاكل "تجاوز الحد المسموح"
                </h4>
            </div>
            
            <div class="card-body">
                <div class="alert alert-danger">
                    <h5><i class="fas fa-times-circle me-2"></i>هل تواجه هذه الرسالة؟</h5>
                    <p class="mb-0">"تم تجاوز الحد المسموح من الطلبات. حاول مرة أخرى لاحقاً"</p>
                </div>

                <h5 class="mb-3">🚀 حلول فورية (اختر واحد):</h5>

                <!-- الحل الأول: الوضع الآمن جداً -->
                <div class="card mb-3 border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">✅ الحل الأول: الوضع الآمن جداً (موصى به)</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>اضغط الزر أدناه لتطبيق إعدادات آمنة 100%:</strong></p>
                        <button id="applySafeSettings" class="btn btn-success btn-lg">
                            <i class="fas fa-shield-alt me-2"></i>
                            تطبيق الإعدادات الآمنة
                        </button>
                        <small class="d-block text-muted mt-2">
                            سيتم تعيين: 5 أسئلة/دقيقة، تأخير 5 ثواني، 50 رمز لكل إجابة
                        </small>
                    </div>
                </div>

                <!-- الحل الثاني: انتظار -->
                <div class="card mb-3 border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">⏰ الحل الثاني: الانتظار</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>انتظر 5 دقائق ثم حاول مرة أخرى</strong></p>
                        <div class="progress mb-2">
                            <div id="waitProgress" class="progress-bar bg-info" style="width: 0%"></div>
                        </div>
                        <button id="startWaitTimer" class="btn btn-info">
                            <i class="fas fa-clock me-2"></i>
                            بدء العد التنازلي (5 دقائق)
                        </button>
                        <div id="waitMessage" class="mt-2" style="display: none;"></div>
                    </div>
                </div>

                <!-- الحل الثالث: تحقق من الرصيد -->
                <div class="card mb-3 border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">💳 الحل الثالث: تحقق من رصيد OpenAI</h6>
                    </div>
                    <div class="card-body">
                        <p>قد تكون المشكلة في نفاد الرصيد:</p>
                        <ol>
                            <li>اذهب إلى <a href="https://platform.openai.com/account/billing" target="_blank">صفحة الفوترة</a></li>
                            <li>تحقق من الرصيد المتبقي</li>
                            <li>أضف رصيد إذا كان منخفضاً (5$ كافية)</li>
                        </ol>
                        <a href="https://platform.openai.com/account/billing" target="_blank" class="btn btn-warning">
                            <i class="fas fa-external-link-alt me-2"></i>
                            فتح صفحة الفوترة
                        </a>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="mt-4">
                    <h6><i class="fas fa-lightbulb me-2 text-warning"></i>نصائح لتجنب المشكلة مستقبلاً:</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <i class="fas fa-check text-success me-2"></i>
                            استخدم الوضع المحافظ في الإعدادات
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success me-2"></i>
                            انتظر 3-5 ثواني بين كل سؤال والآخر
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success me-2"></i>
                            لا ترسل أكثر من 5-8 أسئلة في الدقيقة
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-check text-success me-2"></i>
                            استخدم أسئلة واضحة ومحددة
                        </li>
                    </ul>
                </div>

                <!-- أزرار التنقل -->
                <div class="mt-4 text-center">
                    <a href="{{ url_for('ai_settings') }}" class="btn btn-primary me-2">
                        <i class="fas fa-cogs me-2"></i>
                        الإعدادات المتقدمة
                    </a>
                    <a href="{{ url_for('ai_chat') }}" class="btn btn-secondary">
                        <i class="fas fa-comments me-2"></i>
                        العودة للمحادثة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تطبيق الإعدادات الآمنة
    $('#applySafeSettings').click(function() {
        const button = $(this);
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري التطبيق...');
        
        // إرسال الإعدادات الآمنة
        $.ajax({
            url: '/update_ai_settings',
            method: 'POST',
            data: {
                'enabled': 'on',
                'provider': 'openai',
                'model': 'gpt-3.5-turbo',
                'max_tokens': '50',
                'temperature': '0.5',
                'rate_limit_delay': '5',
                'max_requests_per_minute': '5',
                'use_exponential_backoff': 'on',
                'conservative_mode': 'on'
            },
            success: function() {
                button.removeClass('btn-success').addClass('btn-success')
                      .html('<i class="fas fa-check me-2"></i>تم التطبيق بنجاح!');
                
                // إظهار رسالة نجاح
                const successAlert = `
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>تم!</strong> تم تطبيق الإعدادات الآمنة. يمكنك الآن استخدام المحادثة بأمان.
                        <div class="mt-2">
                            <a href="{{ url_for('ai_chat') }}" class="btn btn-sm btn-success">
                                جرب المحادثة الآن
                            </a>
                        </div>
                    </div>
                `;
                button.parent().append(successAlert);
            },
            error: function() {
                button.prop('disabled', false)
                      .removeClass('btn-success').addClass('btn-danger')
                      .html('<i class="fas fa-times me-2"></i>فشل التطبيق');
            }
        });
    });
    
    // العد التنازلي للانتظار
    $('#startWaitTimer').click(function() {
        const button = $(this);
        const progress = $('#waitProgress');
        const message = $('#waitMessage');
        
        button.prop('disabled', true);
        message.show();
        
        let timeLeft = 300; // 5 minutes in seconds
        const interval = setInterval(function() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            const progressPercent = ((300 - timeLeft) / 300) * 100;
            
            progress.css('width', progressPercent + '%');
            message.html(`
                <i class="fas fa-clock me-2"></i>
                الوقت المتبقي: ${minutes}:${seconds.toString().padStart(2, '0')}
            `);
            
            timeLeft--;
            
            if (timeLeft < 0) {
                clearInterval(interval);
                progress.css('width', '100%').removeClass('bg-info').addClass('bg-success');
                message.html(`
                    <i class="fas fa-check-circle me-2 text-success"></i>
                    انتهى الانتظار! يمكنك الآن المحاولة مرة أخرى.
                    <a href="{{ url_for('ai_chat') }}" class="btn btn-sm btn-success ms-2">
                        جرب الآن
                    </a>
                `);
                button.prop('disabled', false).html('<i class="fas fa-redo me-2"></i>إعادة العد');
            }
        }, 1000);
    });
});
</script>
{% endblock %}
