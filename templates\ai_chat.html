{% extends "base.html" %}

{% block title %}المحادثة مع الذكاء الاصطناعي{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header bg-gradient text-white text-center">
                <h3 class="mb-0">
                    <i class="fas fa-robot me-2"></i>
                    المحادثة مع الذكاء الاصطناعي
                </h3>
                <p class="mb-0 mt-2">اسأل أي سؤال وسأجيبك بذكاء!</p>
            </div>
            
            <div class="card-body p-0">
                <!-- منطقة المحادثة -->
                <div id="chatArea" class="chat-area p-4">
                    <div class="welcome-message text-center mb-4">
                        <div class="ai-avatar mb-3">
                            <i class="fas fa-robot fa-3x text-primary"></i>
                        </div>
                        <h5>مرحباً! أنا مساعدك الذكي</h5>
                        <p class="text-muted">يمكنني الإجابة على أسئلتك في مختلف المجالات</p>

                        <!-- رسالة تحذيرية -->
                        <div class="alert alert-info mt-3" style="font-size: 0.9rem;">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة مهمة:</strong> تم تفعيل الوضع المحافظ لتجنب مشاكل Rate Limits.
                            <br>
                            <small>انتظر 4 ثواني بين كل سؤال • الحد الأقصى: 8 أسئلة/دقيقة</small>
                        </div>
                    </div>
                </div>
                
                <!-- منطقة الكتابة -->
                <div class="chat-input-area border-top p-3">
                    <div class="row g-2">
                        <div class="col">
                            <input type="text" id="questionInput" class="form-control form-control-lg"
                                   placeholder="اكتب سؤالك هنا..." maxlength="500">
                            <div id="cooldownMessage" class="text-muted small mt-1" style="display: none;">
                                <i class="fas fa-clock me-1"></i>
                                انتظر <span id="cooldownTimer">0</span> ثانية قبل السؤال التالي
                            </div>
                        </div>
                        <div class="col-auto">
                            <button id="sendBtn" class="btn btn-primary btn-lg px-4">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- أزرار الأسئلة السريعة -->
                    <div class="quick-questions mt-3">
                        <small class="text-muted">أسئلة سريعة:</small>
                        <div class="mt-2">
                            <button class="btn btn-outline-primary btn-sm me-2 mb-2 quick-question"
                                    data-question="ما هي عاصمة مصر؟">
                                ما هي عاصمة مصر؟
                            </button>
                            <button class="btn btn-outline-primary btn-sm me-2 mb-2 quick-question"
                                    data-question="كم عدد كواكب النظام الشمسي؟">
                                كم عدد كواكب النظام الشمسي؟
                            </button>
                            <button class="btn btn-outline-primary btn-sm me-2 mb-2 quick-question"
                                    data-question="ما هو أكبر محيط في العالم؟">
                                ما هو أكبر محيط في العالم؟
                            </button>
                            <button class="btn btn-outline-primary btn-sm me-2 mb-2 quick-question"
                                    data-question="من اخترع الهاتف؟">
                                من اخترع الهاتف؟
                            </button>
                        </div>
                    </div>

                    <!-- تنبيه معدل الطلبات -->
                    <div class="alert alert-warning mt-3" style="font-size: 0.85rem;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحديث مهم:</strong> تم تفعيل الوضع المحافظ لتجنب مشاكل Rate Limits.
                        الحد الأقصى: 8 أسئلة في الدقيقة مع تأخير 3 ثواني بين كل سؤال.
                        <div class="mt-2">
                            <a href="{{ url_for('rate_limit_fix') }}" class="alert-link me-2 fw-bold">
                                <i class="fas fa-tools me-1"></i>حل سريع
                            </a>
                            <a href="{{ url_for('ai_settings') }}" class="alert-link me-2">
                                <i class="fas fa-cogs me-1"></i>الإعدادات
                            </a>
                            <a href="{{ url_for('ai_help') }}" class="alert-link">
                                <i class="fas fa-question-circle me-1"></i>مساعدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات المحادثة -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-comments fa-2x text-primary mb-2"></i>
                        <h6 id="chatCount">0</h6>
                        <small class="text-muted">رسائل في هذه الجلسة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-success mb-2"></i>
                        <h6 id="responseTime">-</h6>
                        <small class="text-muted">متوسط وقت الاستجابة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-brain fa-2x text-warning mb-2"></i>
                        <h6>متقدم</h6>
                        <small class="text-muted">مستوى الذكاء الاصطناعي</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.chat-area {
    height: 400px;
    overflow-y: auto;
    background: #f8f9fa;
}

.chat-message {
    margin-bottom: 20px;
    animation: fadeInUp 0.5s ease;
}

.user-message {
    text-align: right;
}

.ai-message {
    text-align: left;
}

.message-bubble {
    display: inline-block;
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
}

.user-bubble {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-bottom-right-radius: 4px;
}

.ai-bubble {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 4px;
}

.ai-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    float: left;
}

.error-avatar {
    background: linear-gradient(45deg, #dc3545, #c82333) !important;
}

.error-bubble {
    background: #f8d7da !important;
    border-color: #f5c6cb !important;
    color: #721c24 !important;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    float: right;
}

.typing-indicator {
    display: none;
    text-align: left;
    margin-bottom: 20px;
}

.typing-dots {
    display: inline-block;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 18px;
    padding: 12px 16px;
    margin-left: 50px;
}

.typing-dots span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    margin: 0 2px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.quick-question:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.chat-input-area {
    background: white;
}

#questionInput:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}
</style>
{% endblock %}

{% block scripts %}
<script>
let chatCount = 0;
let responseTimes = [];
let lastRequestTime = 0;
let cooldownInterval = null;
const RATE_LIMIT_DELAY = 4000; // 4 seconds in milliseconds (3 + 1 buffer)
const MAX_REQUESTS_PER_MINUTE = 8;

$(document).ready(function() {
    // التركيز على حقل الإدخال
    $('#questionInput').focus();

    // إرسال السؤال عند الضغط على Enter
    $('#questionInput').keypress(function(e) {
        if (e.which === 13 && !e.shiftKey) {
            e.preventDefault();
            sendQuestion();
        }
    });

    // إرسال السؤال عند الضغط على الزر
    $('#sendBtn').click(sendQuestion);

    // الأسئلة السريعة
    $('.quick-question').click(function() {
        const question = $(this).data('question');
        $('#questionInput').val(question);
        sendQuestion();
    });

    // تحديث حالة الزر كل ثانية
    setInterval(updateSendButtonState, 100);
});

function updateSendButtonState() {
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    const remainingTime = Math.max(0, RATE_LIMIT_DELAY - timeSinceLastRequest);

    if (remainingTime > 0) {
        $('#sendBtn').prop('disabled', true);
        $('#cooldownMessage').show();
        $('#cooldownTimer').text(Math.ceil(remainingTime / 1000));
    } else {
        $('#sendBtn').prop('disabled', false);
        $('#cooldownMessage').hide();
    }
}

function sendQuestion() {
    const question = $('#questionInput').val().trim();
    if (!question) return;

    // التحقق من Rate Limit
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
        const remainingTime = Math.ceil((RATE_LIMIT_DELAY - timeSinceLastRequest) / 1000);
        alert(`يرجى الانتظار ${remainingTime} ثانية قبل إرسال السؤال التالي`);
        return;
    }

    // تسجيل وقت الطلب
    lastRequestTime = now;

    // إضافة رسالة المستخدم
    addUserMessage(question);

    // مسح حقل الإدخال
    $('#questionInput').val('');

    // إظهار مؤشر الكتابة
    showTypingIndicator();

    // تعطيل الزر
    $('#sendBtn').prop('disabled', true);

    const startTime = Date.now();
    
    // إرسال السؤال للخادم
    $.ajax({
        url: '/ask_ai',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({question: question}),
        success: function(response) {
            const endTime = Date.now();
            const responseTime = endTime - startTime;

            hideTypingIndicator();

            // التحقق من وجود خطأ في الإجابة
            if (response.answer.includes('⏳') || response.answer.includes('❌')) {
                addAiMessage(response.answer, response.timestamp, 'error');
            } else {
                addAiMessage(response.answer, response.timestamp);
                updateStats(responseTime);
            }

            $('#questionInput').focus();
        },
        error: function(xhr, status, error) {
            hideTypingIndicator();
            let errorMessage = 'عذراً، حدث خطأ في الحصول على الإجابة.';

            if (xhr.status === 429) {
                errorMessage = '⏳ تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار دقيقة واحدة.';
            } else if (xhr.status === 401) {
                errorMessage = '❌ خطأ في المصادقة. تحقق من مفتاح API في الإعدادات.';
            } else if (xhr.status === 0) {
                errorMessage = '🌐 خطأ في الاتصال. تحقق من اتصال الإنترنت.';
            }

            addAiMessage(errorMessage, new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}), 'error');
            $('#questionInput').focus();
        }
    });
}

function addUserMessage(message) {
    const time = new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
    const messageHtml = `
        <div class="chat-message user-message">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="message-bubble user-bubble">
                ${message}
                <div class="message-time">${time}</div>
            </div>
            <div class="clearfix"></div>
        </div>
    `;
    
    $('#chatArea').append(messageHtml);
    scrollToBottom();
}

function addAiMessage(message, time, type = 'normal') {
    const isError = type === 'error' || message.includes('❌') || message.includes('⏳') || message.includes('🚫');
    const bubbleClass = isError ? 'ai-bubble error-bubble' : 'ai-bubble';
    const iconClass = isError ? 'fas fa-exclamation-triangle' : 'fas fa-robot';

    const messageHtml = `
        <div class="chat-message ai-message">
            <div class="ai-avatar ${isError ? 'error-avatar' : ''}">
                <i class="${iconClass}"></i>
            </div>
            <div class="message-bubble ${bubbleClass}">
                ${message}
                <div class="message-time">${time}</div>
            </div>
            <div class="clearfix"></div>
        </div>
    `;

    $('#chatArea').append(messageHtml);
    scrollToBottom();
}

function showTypingIndicator() {
    const typingHtml = `
        <div class="typing-indicator">
            <div class="ai-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div class="clearfix"></div>
        </div>
    `;
    
    $('#chatArea').append(typingHtml);
    $('.typing-indicator').show();
    scrollToBottom();
}

function hideTypingIndicator() {
    $('.typing-indicator').remove();
}

function scrollToBottom() {
    $('#chatArea').scrollTop($('#chatArea')[0].scrollHeight);
}

function updateStats(responseTime) {
    chatCount++;
    responseTimes.push(responseTime);
    
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    
    $('#chatCount').text(chatCount);
    $('#responseTime').text((avgResponseTime / 1000).toFixed(1) + 'ث');
}
</script>
{% endblock %}
