# إعدادات الذكاء الاصطناعي
import os
import json

class AIConfig:
    def __init__(self):
        self.config_file = 'ai_settings.json'
        self.default_config = {
            'enabled': False,  # مُعطل افتراضياً حتى يتم إدخال مفتاح API
            'provider': 'openai',
            'api_key': '',
            'model': 'gpt-3.5-turbo',
            'max_tokens': 150,
            'temperature': 0.7,
            'timeout': 30,
            'max_retries': 3
        }
        self.config = self.load_config()
    
    def load_config(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # دمج الإعدادات الافتراضية مع المحفوظة
                    merged_config = self.default_config.copy()
                    merged_config.update(config)
                    return merged_config
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الذكاء الاصطناعي: {e}")
        
        return self.default_config.copy()
    
    def save_config(self):
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الذكاء الاصطناعي: {e}")
            return False
    
    def get(self, key, default=None):
        """الحصول على قيمة إعداد"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """تعيين قيمة إعداد"""
        self.config[key] = value
    
    def update(self, new_config):
        """تحديث عدة إعدادات"""
        self.config.update(new_config)
    
    def is_enabled(self):
        """التحقق من تفعيل الذكاء الاصطناعي"""
        return self.config.get('enabled', False) and bool(self.config.get('api_key', '').strip())
    
    def get_headers(self):
        """الحصول على headers للطلبات"""
        if self.config['provider'] == 'openai':
            return {
                'Authorization': f'Bearer {self.config["api_key"]}',
                'Content-Type': 'application/json'
            }
        return {}
    
    def get_api_url(self):
        """الحصول على رابط API"""
        if self.config['provider'] == 'openai':
            return 'https://api.openai.com/v1/chat/completions'
        elif self.config['provider'] == 'gemini':
            return 'https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent'
        return None
    
    def validate_config(self):
        """التحقق من صحة الإعدادات"""
        errors = []
        
        if not self.config.get('api_key', '').strip():
            errors.append('مفتاح API مطلوب')
        
        if self.config.get('max_tokens', 0) < 10 or self.config.get('max_tokens', 0) > 4000:
            errors.append('عدد الرموز يجب أن يكون بين 10 و 4000')
        
        if self.config.get('temperature', 0) < 0 or self.config.get('temperature', 0) > 2:
            errors.append('درجة الحرارة يجب أن تكون بين 0 و 2')
        
        return errors

# إنشاء مثيل عام للإعدادات
ai_config = AIConfig()

# دوال مساعدة للوصول السريع
def get_ai_config():
    """الحصول على إعدادات الذكاء الاصطناعي"""
    return ai_config

def is_ai_enabled():
    """التحقق من تفعيل الذكاء الاصطناعي"""
    return ai_config.is_enabled()

def save_ai_config():
    """حفظ إعدادات الذكاء الاصطناعي"""
    return ai_config.save_config()

def update_ai_config(new_config):
    """تحديث إعدادات الذكاء الاصطناعي"""
    ai_config.update(new_config)
    return ai_config.save_config()

# إعدادات الرسائل النظامية
SYSTEM_MESSAGES = {
    'general': 'أنت مساعد ذكي يجيب على الأسئلة باللغة العربية بشكل دقيق ومفيد. كن مختصراً وواضحاً في إجاباتك.',
    'quiz_generator': 'أنت خبير في إنشاء الأسئلة التعليمية باللغة العربية. أنشئ أسئلة واضحة ومفيدة مع إجابات دقيقة.',
    'educational': 'أنت معلم خبير يشرح المفاهيم بطريقة بسيطة ومفهومة باللغة العربية.',
    'conversational': 'أنت مساعد ودود ومفيد يحب المحادثة باللغة العربية ويساعد المستخدمين بكل ما يحتاجونه.'
}

def get_system_message(message_type='general'):
    """الحصول على رسالة النظام حسب النوع"""
    return SYSTEM_MESSAGES.get(message_type, SYSTEM_MESSAGES['general'])
