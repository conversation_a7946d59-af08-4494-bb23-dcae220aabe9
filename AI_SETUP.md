# دليل إعداد الذكاء الاصطناعي

## نظرة عامة

تطبيق الأسئلة والأجوبة يدعم الآن الذكاء الاصطناعي لتوفير تجربة تفاعلية متقدمة. يمكنك:

- 💬 **المحادثة الذكية**: اسأل أي سؤال واحصل على إجابة فورية
- 🧠 **توليد الأسئلة**: إنشاء أسئلة جديدة تلقائياً
- 🎯 **إجابات دقيقة**: إجابات مخصصة باللغة العربية

## الحصول على مفتاح API

### OpenAI (الموصى به)

1. **إنشاء حساب**:
   - انتقل إلى [OpenAI Platform](https://platform.openai.com/)
   - أنشئ حساباً جديداً أو سجل الدخول

2. **الحصول على مفتاح API**:
   - اذهب إلى [API Keys](https://platform.openai.com/api-keys)
   - اضغط على "Create new secret key"
   - انسخ المفتاح واحفظه في مكان آمن

3. **إضافة رصيد**:
   - انتقل إلى [Billing](https://platform.openai.com/account/billing)
   - أضف طريقة دفع وبعض الرصيد (5$ كافية للبداية)

### Google Gemini (قيد التطوير)

- دعم Gemini سيتم إضافته في التحديثات القادمة

## إعداد التطبيق

### 1. تفعيل الذكاء الاصطناعي

1. شغّل التطبيق واذهب إلى "إعدادات الذكاء الاصطناعي"
2. فعّل خيار "تفعيل الذكاء الاصطناعي"
3. اختر "OpenAI" كمزود الخدمة

### 2. إدخال مفتاح API

1. الصق مفتاح API في الحقل المخصص
2. اختر النموذج المناسب:
   - **GPT-3.5 Turbo**: سريع واقتصادي (موصى به للبداية)
   - **GPT-4**: أكثر دقة وذكاء (أغلى)
   - **GPT-4 Turbo**: متوازن بين السرعة والجودة

### 3. ضبط الإعدادات

- **الحد الأقصى للرموز**: 150-300 (يحدد طول الإجابة)
- **درجة الإبداع**: 0.7 (متوازن بين الدقة والإبداع)

### 4. اختبار الاتصال

- اضغط على "اختبار الاتصال" للتأكد من عمل الإعدادات
- احفظ الإعدادات

## استخدام الميزات

### المحادثة الذكية

1. انتقل إلى "المحادثة مع الذكاء الاصطناعي"
2. اكتب سؤالك في الحقل المخصص
3. اضغط إرسال أو Enter
4. احصل على إجابة فورية

**أمثلة على الأسئلة**:
- "ما هي عاصمة فرنسا؟"
- "اشرح لي نظرية النسبية بشكل مبسط"
- "ما هي فوائد الرياضة؟"

### توليد الأسئلة

1. انتقل إلى "الأسئلة المولدة بالذكاء الاصطناعي"
2. اضغط على "توليد سؤال جديد"
3. اختر الموضوع ومستوى الصعوبة
4. احصل على سؤال جديد مع الإجابة

**المواضيع المتاحة**:
- علوم، تاريخ، جغرافيا
- رياضيات، أدب، رياضة
- تكنولوجيا، فن، طبخ

## التكلفة والاستخدام

### تقدير التكلفة (OpenAI)

- **GPT-3.5 Turbo**: ~$0.002 لكل 1000 رمز
- **GPT-4**: ~$0.03 لكل 1000 رمز
- **GPT-4 Turbo**: ~$0.01 لكل 1000 رمز

### نصائح لتوفير التكلفة

1. **ابدأ بـ GPT-3.5 Turbo**: أرخص وكافي لمعظم الاستخدامات
2. **قلل عدد الرموز**: استخدم 100-200 رمز للإجابات القصيرة
3. **راقب الاستخدام**: تحقق من استهلاكك في لوحة OpenAI

## الأمان والخصوصية

### حماية مفتاح API

- ✅ **لا تشارك** مفتاح API مع أحد
- ✅ **احفظه بأمان** في مكان آمن
- ✅ **أعد إنشاؤه** إذا تم تسريبه
- ✅ **استخدم حدود الإنفاق** في حساب OpenAI

### الخصوصية

- 🔒 **البيانات محلية**: جميع المحادثات محفوظة محلياً
- 🔒 **لا مشاركة**: لا نشارك بياناتك مع أطراف ثالثة
- 🔒 **تشفير الاتصال**: جميع الطلبات مشفرة (HTTPS)

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

**"خطأ في المصادقة"**:
- تحقق من صحة مفتاح API
- تأكد من وجود رصيد في حسابك

**"تم تجاوز الحد المسموح"**:
- انتظر قليلاً ثم حاول مرة أخرى
- ارفع حدود الاستخدام في حساب OpenAI

**"خدمة غير متوفرة"**:
- تحقق من اتصال الإنترنت
- تأكد من تفعيل الذكاء الاصطناعي في الإعدادات

**إجابات غير دقيقة**:
- قلل درجة الإبداع (Temperature)
- استخدم نموذج أكثر تقدماً (GPT-4)
- اجعل السؤال أكثر وضوحاً

## الدعم

إذا واجهت أي مشاكل:

1. **تحقق من الإعدادات**: تأكد من صحة جميع الإعدادات
2. **اختبر الاتصال**: استخدم زر "اختبار الاتصال"
3. **راجع الوثائق**: اقرأ هذا الدليل مرة أخرى
4. **تواصل معنا**: أرسل تفاصيل المشكلة

---

**استمتع بتجربة الذكاء الاصطناعي! 🤖✨**
