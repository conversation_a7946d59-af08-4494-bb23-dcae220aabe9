{% extends "base.html" %}

{% block title %}الاختبار - السؤال {{ question_num }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <!-- شريط التقدم -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-bold">السؤال {{ question_num }} من {{ total_questions }}</span>
                    <span class="badge bg-primary">{{ question.category }}</span>
                </div>
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: {{ progress }}%"></div>
                </div>
            </div>
        </div>

        <!-- بطاقة السؤال -->
        <div class="question-card">
            <h3 class="mb-4 text-center">{{ question.question }}</h3>
            
            {% if question.type == 'true_false' %}
                <!-- أسئلة صح وخطأ -->
                <div class="row g-3">
                    <div class="col-md-6">
                        <button class="option-btn" data-answer="true">
                            <i class="fas fa-check-circle me-2 text-success"></i>
                            صحيح
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="option-btn" data-answer="false">
                            <i class="fas fa-times-circle me-2 text-danger"></i>
                            خطأ
                        </button>
                    </div>
                </div>
            {% else %}
                <!-- أسئلة اختيار متعدد -->
                <div class="row g-3">
                    {% for option in question.options %}
                        {% if option %}
                            <div class="col-12">
                                <button class="option-btn" data-answer="{{ option }}">
                                    <span class="fw-bold me-2">{{ loop.index }}.</span>
                                    {{ option }}
                                </button>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endif %}

            <div class="text-center mt-4">
                <button id="submitBtn" class="btn btn-primary btn-lg px-5" disabled>
                    <i class="fas fa-paper-plane me-2"></i>
                    إرسال الإجابة
                </button>
            </div>
        </div>

        <!-- رسالة النتيجة -->
        <div id="resultMessage" class="alert" style="display: none;">
            <div class="d-flex align-items-center">
                <i id="resultIcon" class="fas fa-2x me-3"></i>
                <div>
                    <h5 id="resultTitle" class="mb-1"></h5>
                    <p id="resultText" class="mb-0"></p>
                </div>
            </div>
        </div>

        <!-- أزرار التنقل -->
        <div class="text-center mt-4">
            <button id="nextBtn" class="btn btn-success btn-lg px-5" style="display: none;">
                <i class="fas fa-arrow-left me-2"></i>
                السؤال التالي
            </button>
            <button id="finishBtn" class="btn btn-warning btn-lg px-5" style="display: none;">
                <i class="fas fa-flag-checkered me-2"></i>
                إنهاء الاختبار
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedAnswer = null;
let answered = false;

$(document).ready(function() {
    // تأثير ظهور السؤال
    $('.question-card').hide().slideDown(500);
    
    // التعامل مع اختيار الإجابة
    $('.option-btn').click(function() {
        if (answered) return;
        
        $('.option-btn').removeClass('selected');
        $(this).addClass('selected');
        selectedAnswer = $(this).data('answer');
        $('#submitBtn').prop('disabled', false);
    });
    
    // إرسال الإجابة
    $('#submitBtn').click(function() {
        if (!selectedAnswer || answered) return;
        
        answered = true;
        $(this).prop('disabled', true);
        $('.option-btn').prop('disabled', true);
        
        // إرسال الإجابة للخادم
        $.ajax({
            url: '/submit_answer',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({answer: selectedAnswer}),
            success: function(response) {
                showResult(response);
            },
            error: function() {
                alert('حدث خطأ في إرسال الإجابة');
            }
        });
    });
    
    // الانتقال للسؤال التالي
    $('#nextBtn').click(function() {
        window.location.reload();
    });
    
    // إنهاء الاختبار
    $('#finishBtn').click(function() {
        window.location.href = '/results';
    });
});

function showResult(response) {
    const resultDiv = $('#resultMessage');
    const resultIcon = $('#resultIcon');
    const resultTitle = $('#resultTitle');
    const resultText = $('#resultText');
    
    // تمييز الإجابات
    $('.option-btn').each(function() {
        const btnAnswer = $(this).data('answer');
        if (btnAnswer === response.correct_answer) {
            $(this).addClass('correct-answer');
        } else if (btnAnswer === selectedAnswer && !response.correct) {
            $(this).addClass('wrong-answer');
        }
    });
    
    if (response.correct) {
        resultDiv.removeClass('alert-danger').addClass('alert-success');
        resultIcon.removeClass('fa-times-circle').addClass('fa-check-circle text-success');
        resultTitle.text('إجابة صحيحة! 🎉');
        resultText.text('أحسنت! لقد أجبت بشكل صحيح.');
    } else {
        resultDiv.removeClass('alert-success').addClass('alert-danger');
        resultIcon.removeClass('fa-check-circle').addClass('fa-times-circle text-danger');
        resultTitle.text('إجابة خاطئة 😔');
        resultText.text('الإجابة الصحيحة هي: ' + response.correct_answer);
    }
    
    resultDiv.slideDown(300);
    
    // إظهار زر المتابعة
    setTimeout(function() {
        if (response.next_question) {
            $('#nextBtn').fadeIn(300);
        } else {
            $('#finishBtn').fadeIn(300);
        }
    }, 1000);
}
</script>
{% endblock %}
