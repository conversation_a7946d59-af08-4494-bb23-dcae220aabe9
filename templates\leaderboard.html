{% extends "base.html" %}

{% block title %}لوحة المتصدرين - تطبيق الأسئلة والأجوبة{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header bg-primary text-white text-center">
                <h2 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    لوحة المتصدرين
                </h2>
                <p class="mb-0 mt-2">أفضل 10 نتائج</p>
            </div>
            <div class="card-body p-0">
                {% if results %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center">المرتبة</th>
                                    <th>اسم المتسابق</th>
                                    <th class="text-center">النتيجة</th>
                                    <th class="text-center">النسبة المئوية</th>
                                    <th class="text-center">الوقت المستغرق</th>
                                    <th class="text-center">تاريخ الاختبار</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for result in results %}
                                    <tr class="{% if loop.index <= 3 %}table-warning{% endif %}">
                                        <td class="text-center">
                                            {% if loop.index == 1 %}
                                                <i class="fas fa-crown text-warning fa-lg"></i>
                                                <span class="fw-bold text-warning">1</span>
                                            {% elif loop.index == 2 %}
                                                <i class="fas fa-medal text-secondary fa-lg"></i>
                                                <span class="fw-bold text-secondary">2</span>
                                            {% elif loop.index == 3 %}
                                                <i class="fas fa-award text-warning fa-lg"></i>
                                                <span class="fw-bold" style="color: #CD7F32;">3</span>
                                            {% else %}
                                                <span class="fw-bold">{{ loop.index }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2">
                                                    {{ result[0][0].upper() }}
                                                </div>
                                                <span class="fw-bold">{{ result[0] }}</span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-primary fs-6">
                                                {{ result[1] }}/{{ result[2] }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar 
                                                    {% if result[3] >= 90 %}bg-success
                                                    {% elif result[3] >= 80 %}bg-info
                                                    {% elif result[3] >= 70 %}bg-warning
                                                    {% else %}bg-danger{% endif %}" 
                                                    role="progressbar" 
                                                    style="width: {{ result[3] }}%">
                                                    {{ "%.1f"|format(result[3]) }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            {% set minutes = result[4] // 60 %}
                                            {% set seconds = result[4] % 60 %}
                                            <span class="badge bg-secondary">
                                                {{ minutes }}:{{ "%02d"|format(seconds) }}
                                            </span>
                                        </td>
                                        <td class="text-center text-muted">
                                            {{ result[5][:10] }}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center p-5">
                        <i class="fas fa-trophy fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد نتائج بعد</h4>
                        <p class="text-muted">كن أول من يخوض الاختبار!</p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-play me-2"></i>
                            ابدأ الاختبار الآن
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- إحصائيات إضافية -->
        {% if results %}
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h4>{{ results|length }}</h4>
                            <p class="text-muted">متسابق</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-percentage fa-3x text-success mb-3"></i>
                            <h4>{{ "%.1f"|format(results[0][3]) }}%</h4>
                            <p class="text-muted">أعلى نسبة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                            <h4>
                                {% set best_time = results|selectattr(3, 'equalto', results[0][3])|map(attribute=4)|min %}
                                {% set minutes = best_time // 60 %}
                                {% set seconds = best_time % 60 %}
                                {{ minutes }}:{{ "%02d"|format(seconds) }}
                            </h4>
                            <p class="text-muted">أسرع وقت</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <div class="text-center mt-4">
            <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-home me-2"></i>
                العودة للرئيسية
            </a>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
    font-weight: bold;
    font-size: 0.8rem;
}
</style>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // تأثير ظهور الجدول
    $('.card').hide().fadeIn(1000);
    
    // تأثير ظهور الصفوف
    $('tbody tr').each(function(index) {
        $(this).hide().delay(200 * index).fadeIn(500);
    });
    
    // تأثير hover للصفوف
    $('tbody tr').hover(
        function() {
            $(this).addClass('shadow-sm');
        },
        function() {
            $(this).removeClass('shadow-sm');
        }
    );
    
    // تأثير للأيقونات
    $('.fa-crown, .fa-medal, .fa-award').addClass('animate__animated animate__pulse animate__infinite');
});
</script>
{% endblock %}
