# تطبيق الأسئلة والأجوبة - Flask Quiz App

تطبيق ويب تفاعلي للأسئلة والأجوبة باللغة العربية مبني بـ Flask مع دعم قاعدة بيانات SQLite.

## المميزات

- 🎯 **أسئلة متنوعة**: دعم أسئلة صح/خطأ والاختيار من متعدد
- 🤖 **الذكاء الاصطناعي**: محادثة ذكية وتوليد أسئلة تلقائياً
- 🌐 **دعم اللغة العربية**: واجهة مستخدم باللغة العربية بالكامل
- ⚡ **قاعدة بيانات سريعة**: استخدام SQLite للأداء السريع
- 📊 **لوحة متصدرين**: عرض أفضل النتائج
- 🎨 **تصميم جذاب**: واجهة مستخدم حديثة ومتجاوبة
- ⏱️ **توقيت الاختبارات**: قياس الوقت المستغرق
- 📈 **إحصائيات مفصلة**: عرض النتائج والنسب المئوية
- 💬 **محادثة ذكية**: اسأل أي سؤال واحصل على إجابة فورية
- 🧠 **توليد أسئلة**: إنشاء أسئلة جديدة باستخدام الذكاء الاصطناعي

## متطلبات التشغيل

- Python 3.7 أو أحدث
- Flask 2.3.3
- Werkzeug 2.3.7
- Requests 2.31.0
- مفتاح API من OpenAI (اختياري للذكاء الاصطناعي)

## طريقة التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق

```bash
python app.py
```

### 3. فتح التطبيق

افتح المتصفح وانتقل إلى: `http://localhost:5000`

### 4. إعداد الذكاء الاصطناعي (اختياري)

لتفعيل ميزات الذكاء الاصطناعي:

1. احصل على مفتاح API من [OpenAI](https://platform.openai.com/api-keys)
2. انتقل إلى صفحة "إعدادات الذكاء الاصطناعي" في التطبيق
3. أدخل مفتاح API وفعّل الخدمة
4. اختر النموذج المناسب (GPT-3.5 Turbo موصى به للبداية)
5. احفظ الإعدادات

## هيكل المشروع

```
quiz-app/
├── app.py                 # الملف الرئيسي للتطبيق
├── requirements.txt       # متطلبات Python
├── README.md             # ملف التوثيق
├── quiz_app.db           # قاعدة البيانات (يتم إنشاؤها تلقائياً)
├── templates/            # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── index.html        # الصفحة الرئيسية
│   ├── quiz.html         # صفحة الاختبار
│   ├── results.html      # صفحة النتائج
│   └── leaderboard.html  # لوحة المتصدرين
└── static/              # الملفات الثابتة (CSS, JS, صور)
```

## كيفية الاستخدام

### 1. بدء اختبار جديد
- أدخل اسمك
- اختر عدد الأسئلة (5-20 سؤال)
- اضغط "ابدأ الاختبار"

### 2. الإجابة على الأسئلة
- اقرأ السؤال بعناية
- اختر الإجابة المناسبة
- اضغط "إرسال الإجابة"
- شاهد النتيجة الفورية

### 3. مشاهدة النتائج
- احصل على نتيجتك النهائية
- شاهد الإحصائيات المفصلة
- قارن أداءك مع الآخرين

## ميزات الذكاء الاصطناعي

### 1. المحادثة الذكية
- اسأل أي سؤال واحصل على إجابة فورية
- دعم كامل للغة العربية
- إجابات دقيقة ومفيدة
- حفظ تاريخ المحادثات

### 2. توليد الأسئلة التلقائي
- إنشاء أسئلة جديدة في مواضيع مختلفة
- اختيار مستوى الصعوبة
- أسئلة متنوعة وذكية
- حفظ الأسئلة المولدة

### 3. الإعدادات المتقدمة
- اختيار مزود الخدمة (OpenAI, Gemini)
- تخصيص النموذج المستخدم
- التحكم في طول الإجابات
- ضبط مستوى الإبداع

## إضافة أسئلة جديدة

يمكنك إضافة أسئلة جديدة عبر تعديل دالة `add_sample_questions()` في ملف `app.py`:

```python
# مثال لسؤال صح/خطأ
{
    'question': 'نص السؤال هنا',
    'type': 'true_false',
    'correct': 'true',  # أو 'false'
    'category': 'اسم الفئة'
}

# مثال لسؤال اختيار متعدد
{
    'question': 'نص السؤال هنا',
    'type': 'multiple_choice',
    'correct': 'الإجابة الصحيحة',
    'options': ['خيار 1', 'خيار 2', 'خيار 3', 'خيار 4'],
    'category': 'اسم الفئة'
}
```

## قاعدة البيانات

التطبيق يستخدم SQLite مع جدولين رئيسيين:

### جدول الأسئلة (questions)
- `id`: معرف فريد
- `question`: نص السؤال
- `question_type`: نوع السؤال (true_false أو multiple_choice)
- `correct_answer`: الإجابة الصحيحة
- `option_a, option_b, option_c, option_d`: خيارات الإجابة
- `difficulty`: مستوى الصعوبة
- `category`: فئة السؤال

### جدول النتائج (results)
- `id`: معرف فريد
- `user_name`: اسم المستخدم
- `score`: عدد الإجابات الصحيحة
- `total_questions`: إجمالي الأسئلة
- `percentage`: النسبة المئوية
- `time_taken`: الوقت المستغرق بالثواني
- `completed_at`: تاريخ ووقت إكمال الاختبار

## التخصيص

### تغيير الألوان والتصميم
يمكنك تعديل ملف `templates/base.html` لتخصيص:
- الألوان الأساسية
- الخطوط
- التأثيرات البصرية

### إضافة فئات جديدة
أضف فئات جديدة للأسئلة عبر تعديل خاصية `category` في الأسئلة.

### تخصيص عدد الأسئلة
عدّل خيارات عدد الأسئلة في ملف `templates/index.html`.

## الأمان

⚠️ **مهم للإنتاج**:
- غيّر `app.secret_key` في ملف `app.py`
- استخدم قاعدة بيانات أكثر قوة مثل PostgreSQL أو MySQL
- أضف التحقق من صحة المدخلات
- استخدم HTTPS

## المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة أسئلة جديدة
- تحسين التصميم
- إضافة مميزات جديدة
- إصلاح الأخطاء

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الحر.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، لا تتردد في التواصل!

---

**استمتع بالتعلم! 🎓**
